<template>
  <div class="video-share-container">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-state">
      <el-icon class="loading-icon"><Loading /></el-icon>
      <span>加载视频信息...</span>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-state">
      <el-icon class="error-icon"><Warning /></el-icon>
      <div class="error-text">{{ error }}</div>
      <el-button @click="loadVideoData" type="primary">重试</el-button>
    </div>

    <!-- 视频内容 -->
    <div v-else-if="videoData" class="video-content">
      <!-- 视频播放器容器 -->
      <div class="video-player-container">
        <video
          v-if="videoData.videoUrl"
          :src="videoData.videoUrl"
          controls
          :poster="getCoverImage(videoData)"
          class="video-player"
          @loadedmetadata="onVideoLoaded"
          @error="onVideoError">
        </video>
        <div v-else class="no-video-placeholder">
          <el-icon><VideoPlay /></el-icon>
          <span>视频暂不可用</span>
        </div>

        <!-- 悬浮视频信息 -->
        <div class="video-info-overlay" :class="{ 'hidden': !showVideoInfo }">
          <div class="video-header">
            <h1 class="video-title">{{ videoData.canvasName || `视频作品 #${videoData.id}` }}</h1>
            <div class="video-actions">
              <el-button @click="shareVideo" type="primary" size="small" :icon="Share">
                分享
              </el-button>
              <el-button @click="toggleVideoInfo" text size="small" :icon="showVideoInfo ? ArrowUp : ArrowDown">
              </el-button>
            </div>
          </div>

          <!-- 创作者信息 -->
          <div class="creator-info">
            <div class="creator-avatar">
              <img
                v-if="videoData.userAvatar"
                :src="videoData.userAvatar + '?x-oss-process=image/resize,w_40'"
                :alt="videoData.userNickname"
                @error="handleAvatarError" />
              <el-icon v-else class="default-avatar"><User /></el-icon>
            </div>
            <div class="creator-details">
              <div class="creator-name">{{ videoData.userNickname || '匿名用户' }}</div>
              <div class="video-meta">
                <span class="video-duration" v-if="videoData.videoDuration">
                  {{ formatDuration(videoData.videoDuration) }}
                </span>
                <span class="video-resolution" v-if="videoData.resolution">
                  {{ videoData.resolution }}
                </span>
                <span class="video-ratio" v-if="videoData.ratio">
                  {{ videoData.ratio }}
                </span>
              </div>
              <div class="video-dates">
                <span v-if="videoData.createTime">
                  {{ formatDate(videoData.createTime) }}
                </span>
              </div>
            </div>
          </div>

          <!-- 视频状态 -->
          <div class="video-status" v-if="videoData.status !== undefined">
            <el-tag :type="getStatusType(videoData.status)" size="small">
              {{ videoData.statusDesc || getStatusText(videoData.status) }}
            </el-tag>
          </div>
        </div>
      </div>

      <!-- 右侧视频列表 -->
      <div class="sidebar" :class="{ 'expanded': sidebarExpanded }">
        <div class="sidebar-toggle" @click="toggleSidebar">
          <el-icon class="toggle-icon" :class="{ 'rotated': sidebarExpanded }">
            <ArrowLeft />
          </el-icon>
        </div>

        <div class="sidebar-header" v-show="sidebarExpanded">
          <h3>更多视频</h3>
        </div>
        
        <div v-if="sidebarExpanded" class="video-list">
          <!-- 加载状态 -->
          <div v-if="listLoading" class="list-loading">
            <el-icon class="loading-icon"><Loading /></el-icon>
            <span>加载中...</span>
          </div>
          
          <!-- 视频列表 -->
          <div v-else class="video-items">
            <div 
              v-for="video in videoList" 
              :key="video.id"
              :class="['video-item', { 'active': video.shareCode === currentShareCode }]"
              @click="switchVideo(video)">
              <div class="video-thumbnail">
                <img
                  v-if="getCoverImage(video)"
                  :src="getCoverImage(video) + '?x-oss-process=image/resize,w_120'"
                  :alt="video.canvasName"
                  @error="handleThumbnailError" />
                <el-icon v-else class="no-thumbnail"><VideoPlay /></el-icon>
                <div class="video-duration-overlay" v-if="video.videoDuration">
                  {{ formatDuration(video.videoDuration) }}
                </div>
              </div>
              <div class="video-item-info">
                <div class="video-item-title">
                  {{ video.canvasName || `视频作品 #${video.id}` }}
                </div>
                <div class="video-item-creator">
                  <div class="creator-avatar-small">
                    <img
                      v-if="video.userAvatar"
                      :src="video.userAvatar + '?x-oss-process=image/resize,w_20'"
                      :alt="video.userNickname"
                      @error="handleCreatorAvatarError" />
                    <el-icon v-else class="default-avatar-small"><User /></el-icon>
                  </div>
                  <span class="creator-name-small">{{ video.userNickname || '匿名用户' }}</span>
                </div>
                <div class="video-item-meta">
                  <span class="video-item-date">{{ formatDate(video.createTime) }}</span>
                  <span v-if="video.resolution" class="video-item-resolution">{{ video.resolution }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 加载更多 -->
          <div v-if="hasMore && !listLoading" class="load-more">
            <el-button @click="loadMoreVideos" :loading="loadingMore" text>
              加载更多
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Loading,
  Warning,
  VideoPlay,
  Share,
  User,
  ArrowLeft,
  ArrowUp,
  ArrowDown
} from '@element-plus/icons-vue'
import { getVideoRenderSharedVideo, getVideoRenderSharedList } from '@/api/auth.js'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(true)
const error = ref('')
const videoData = ref(null)
const currentShareCode = ref('')

// 侧边栏相关
const sidebarExpanded = ref(false) // 默认收起
const videoList = ref([])
const listLoading = ref(false)
const loadingMore = ref(false)
const hasMore = ref(true)
const pageIndex = ref(1)
const pageSize = ref(20)

// 视频信息显示控制
const showVideoInfo = ref(true)

// 获取当前视频数据
const loadVideoData = async () => {
  try {
    loading.value = true
    error.value = ''
    
    const shareCode = route.query.shareCode
    if (!shareCode) {
      error.value = '缺少分享码参数'
      return
    }

    currentShareCode.value = shareCode
    const response = await getVideoRenderSharedVideo(shareCode)
    
    if (response && response.success && response.data) {
      videoData.value = response.data
    } else {
      error.value = response?.errMessage || '获取视频信息失败'
    }
  } catch (err) {
    console.error('加载视频数据失败:', err)
    error.value = '网络错误，请稍后重试'
  } finally {
    loading.value = false
  }
}

// 加载视频列表
const loadVideoList = async (isLoadMore = false) => {
  try {
    if (!isLoadMore) {
      listLoading.value = true
      pageIndex.value = 1
    } else {
      loadingMore.value = true
    }

    const response = await getVideoRenderSharedList({
      pageIndex: pageIndex.value,
      pageSize: pageSize.value
    })

    if (response && response.success && response.data) {
      if (isLoadMore) {
        videoList.value = [...videoList.value, ...response.data]
      } else {
        videoList.value = response.data
      }
      
      hasMore.value = response.data.length === pageSize.value
    }
  } catch (err) {
    console.error('加载视频列表失败:', err)
    ElMessage.error('加载视频列表失败')
  } finally {
    listLoading.value = false
    loadingMore.value = false
  }
}

// 加载更多视频
const loadMoreVideos = () => {
  if (loadingMore.value || !hasMore.value) return
  pageIndex.value++
  loadVideoList(true)
}

// 切换视频
const switchVideo = (video) => {
  if (video.shareCode === currentShareCode.value) return
  
  // 更新URL中的shareCode
  router.push({
    path: '/videoShare',
    query: { shareCode: video.shareCode }
  })
}

// 切换侧边栏
const toggleSidebar = () => {
  sidebarExpanded.value = !sidebarExpanded.value
}

// 切换视频信息显示
const toggleVideoInfo = () => {
  showVideoInfo.value = !showVideoInfo.value
}

// 分享视频
const shareVideo = () => {
  if (videoData.value?.shareUrl) {
    navigator.clipboard.writeText(videoData.value.shareUrl).then(() => {
      ElMessage.success('分享链接已复制到剪贴板')
    }).catch(() => {
      ElMessage.error('复制失败，请手动复制链接')
    })
  } else {
    ElMessage.warning('该视频暂无分享链接')
  }
}

// 格式化时长
const formatDuration = (duration) => {
  if (!duration) return '00:00'
  const seconds = Math.floor(duration / 1000)
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
}

// 格式化日期
const formatDate = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
}

// 获取状态类型
const getStatusType = (status) => {
  switch (status) {
    case 1: return 'success'
    case 0: return 'warning'
    default: return 'danger'
  }
}

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 1: return '已完成'
    case 0: return '处理中'
    default: return '失败'
  }
}

// 处理头像错误
const handleAvatarError = (event) => {
  event.target.style.display = 'none'
}

// 处理缩略图错误
const handleThumbnailError = (event) => {
  event.target.style.display = 'none'
}

// 获取封面图片
const getCoverImage = (video) => {
  return video.canvasCoverImage || video.firstFrameUrl
}

// 处理创作者头像错误
const handleCreatorAvatarError = (event) => {
  event.target.style.display = 'none'
}

// 视频加载完成
const onVideoLoaded = () => {
  console.log('视频加载完成')
}

// 视频加载错误
const onVideoError = () => {
  ElMessage.error('视频加载失败')
}

// 监听路由变化
watch(() => route.query.shareCode, (newShareCode) => {
  if (newShareCode && newShareCode !== currentShareCode.value) {
    loadVideoData()
  }
}, { immediate: false })

// 组件挂载
onMounted(() => {
  loadVideoData()
  loadVideoList()
})
</script>

<style scoped>
.video-share-container {
  min-height: 100vh;
  background: #000;
  padding: 0;
  overflow: hidden;
}

/* 加载和错误状态 */
.loading-state,
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  gap: 16px;
}

.loading-icon,
.error-icon {
  font-size: 48px;
  color: #6366f1;
}

.loading-icon {
  animation: rotate 1.5s linear infinite;
}

.error-text {
  font-size: 18px;
  color: #64748b;
  text-align: center;
}

/* 视频内容布局 */
.video-content {
  display: flex;
  height: 100vh;
  position: relative;
}

/* 视频播放器 */
.video-player-container {
  position: relative;
  flex: 1;
  background: #000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-player {
  width: 100%;
  height: 100%;
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  display: block;
}

.no-video-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  color: #64748b;
  font-size: 18px;
  gap: 12px;
}

.no-video-placeholder .el-icon {
  font-size: 64px;
  opacity: 0.5;
}

/* 悬浮视频信息 */
.video-info-overlay {
  position: absolute;
  top: 20px;
  left: 20px;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 16px;
  max-width: 400px;
  color: white;
  transition: all 0.3s ease;
  z-index: 10;
}

.video-info-overlay.hidden {
  transform: translateY(-100%);
  opacity: 0;
  pointer-events: none;
}

.video-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  gap: 12px;
}

.video-title {
  font-size: 18px;
  font-weight: 600;
  color: white;
  margin: 0;
  line-height: 1.3;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.video-actions {
  flex-shrink: 0;
  display: flex;
  gap: 8px;
}

/* 创作者信息 */
.creator-info {
  display: flex;
  gap: 12px;
  margin-bottom: 12px;
}

.creator-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.creator-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.default-avatar {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.7);
}

.creator-details {
  flex: 1;
  min-width: 0;
}

.creator-name {
  font-size: 14px;
  font-weight: 500;
  color: white;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.video-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 4px;
}

.video-meta span {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  background: rgba(255, 255, 255, 0.1);
  padding: 2px 6px;
  border-radius: 3px;
  white-space: nowrap;
}

.video-dates {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.video-dates span {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.6);
}

/* 视频状态 */
.video-status {
  margin-top: 16px;
}

/* 侧边栏 */
.sidebar {
  width: 50px;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  transition: width 0.3s ease;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
}

.sidebar.expanded {
  width: 350px;
}

.sidebar-toggle {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 20;
}

.sidebar-toggle:hover {
  background: rgba(255, 255, 255, 0.2);
}

.toggle-icon {
  color: white;
  font-size: 16px;
  transition: transform 0.3s ease;
}

.toggle-icon.rotated {
  transform: rotate(180deg);
}

.sidebar-header {
  padding: 60px 16px 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: white;
  white-space: nowrap;
}

/* 视频列表 */
.video-list {
  flex: 1;
  overflow-y: auto;
  padding-top: 8px;
}

.list-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 16px;
  gap: 12px;
  color: rgba(255, 255, 255, 0.7);
}

.video-items {
  padding: 8px;
}

.video-item {
  display: flex;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-bottom: 8px;
}

.video-item:hover {
  background: #f8fafc;
}

.video-item.active {
  background: #eff6ff;
  border: 1px solid #3b82f6;
}

.video-thumbnail {
  position: relative;
  width: 80px;
  height: 60px;
  border-radius: 6px;
  overflow: hidden;
  background: #f1f5f9;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.no-thumbnail {
  font-size: 24px;
  color: #94a3b8;
}

.video-duration-overlay {
  position: absolute;
  bottom: 4px;
  right: 4px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 4px;
  border-radius: 3px;
  font-size: 10px;
}

.video-item-info {
  flex: 1;
  min-width: 0;
}

.video-item-title {
  font-size: 14px;
  font-weight: 500;
  color: #1e293b;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.video-item-creator {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 4px;
}

.creator-avatar-small {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  overflow: hidden;
  background: #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.creator-avatar-small img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.default-avatar-small {
  font-size: 8px;
  color: #64748b;
}

.creator-name-small {
  font-size: 12px;
  color: #64748b;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

.video-item-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
}

.video-item-date {
  font-size: 11px;
  color: #94a3b8;
  flex: 1;
}

.video-item-resolution {
  font-size: 10px;
  color: #94a3b8;
  background: #f1f5f9;
  padding: 1px 4px;
  border-radius: 2px;
  flex-shrink: 0;
}

.load-more {
  padding: 16px;
  text-align: center;
}

/* 动画 */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .video-content {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    height: auto;
  }

  .sidebar.expanded {
    width: 100%;
  }

  .video-list {
    height: 300px;
  }
}

@media (max-width: 768px) {
  .video-share-container {
    padding: 10px;
  }

  .video-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .creator-info {
    flex-direction: column;
    text-align: center;
  }

  .creator-avatar {
    align-self: center;
  }

  .video-meta {
    justify-content: center;
  }
}

/* 暗色主题支持 */
body.dark .video-share-container {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
}

body.dark .main-content,
body.dark .sidebar {
  background: #1e293b;
  color: #e2e8f0;
}

body.dark .video-title,
body.dark .creator-name {
  color: #e2e8f0;
}

body.dark .creator-info {
  background: #334155;
}

body.dark .video-item:hover {
  background: #334155;
}

body.dark .video-item.active {
  background: #1e40af;
  border-color: #3b82f6;
}
</style>
